const express = require("express");
const router = express.Router();
const auth = require("../controller/auth.controller");
const passport = require("passport");
const jwt = require("jsonwebtoken");

let BASE_URL = "https://ceddit-one.vercel.app";
if (!process.env.NODE_ENV || process.env.NODE_ENV === "development") {
  BASE_URL = "http://localhost:3000";
}

const buildToken = (user) => {
  return {
    userId: user._id,
    isAdmin: user.isAdmin,
  };
};

router.post("/register", auth.register);
router.post("/login", auth.login);

// Only add Google OAuth routes if Google OAuth is configured
if (process.env.GOOGLE_CLIENT_ID &&
    process.env.GOOGLE_CLIENT_SECRET &&
    process.env.GOOGLE_CLIENT_ID !== 'temp_client_id_for_development' &&
    process.env.GOOGLE_CLIENT_SECRET !== 'temp_client_secret_for_development') {

  router.get("/google", passport.authenticate("google", ["profile", "email"]));
  router.get(
    "/google/callback",
    passport.authenticate("google", {
      failureRedirect: "/login/failed",
      scope: ["email", "profile"],
      session: false,
    }),
    function (req, res) {
      const token = jwt.sign(buildToken(req.user), process.env.TOKEN_KEY);
      if (req.query.origin) {
        res.redirect(
          `${BASE_URL}/#user=${JSON.stringify({
            token,
            username: req.user.username,
            userId: req.user._id,
            isAdmin: req.user.isAdmin,
            user: req.user,
          })}`
        );
      } else {
        res.redirect(
          `${BASE_URL}/#user=${JSON.stringify({
            token,
            username: req.user.username,
            userId: req.user._id,
            isAdmin: req.user.isAdmin,
            user: req.user,
          })}`
        );
      }
    }
  );
} else {
  // Provide fallback routes that return an error message
  router.get("/google", (req, res) => {
    res.status(501).json({
      error: "Google OAuth not configured. Please set up GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET in environment variables."
    });
  });

  router.get("/google/callback", (req, res) => {
    res.status(501).json({
      error: "Google OAuth not configured. Please set up GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET in environment variables."
    });
  });
}

module.exports = router;
