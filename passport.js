const GoogleStrategy = require("passport-google-oauth20").Strategy;
const passport = require("passport");
const User = require("./model/user.model");

// Only configure Google OAuth if credentials are provided and not placeholder values
if (process.env.GOOGLE_CLIENT_ID &&
    process.env.GOOGLE_CLIENT_SECRET &&
    process.env.GOOGLE_CLIENT_ID !== 'temp_client_id_for_development' &&
    process.env.GOOGLE_CLIENT_SECRET !== 'temp_client_secret_for_development') {

  passport.use(
    new GoogleStrategy(
      {
        clientID: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
        callbackURL: "/api/auth/google/callback",
        scope: ["email", "profile"],
      },
      async function (accessToken, refreshToken, profile, done) {
        let user = await User.findOne({ email: profile.emails[0].value });
        if (!user) {
          user = User.create({
            username: profile.displayName,
            email: profile.emails[0].value,
            image: profile.photos[0].value,
          });
          await user.save();
        }
        done(null, user);
      }
    )
  );
} else {
  console.log("Google OAuth not configured - using placeholder credentials. Update GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET in .env file to enable Google authentication.");
}

passport.serializeUser((user, done) => {
  done(null, user);
});

passport.deserializeUser((user, done) => {
  done(null, user);
});
