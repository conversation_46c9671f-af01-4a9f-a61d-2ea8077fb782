services:
  server:
    build:
      context: ./
      dockerfile: Dockerfile
    image: ceddit-server
    container_name: ceddit-node-server
    command: /usr/src/app/node_modules/.bin/nodemon app.js
    volumes:
      - ./:/usr/src/app
      - /usr/src/app/node_modules
    ports:
      - "4000:4000"
    depends_on:
      - mongo
    env_file: ./.env
    environment:
      - NODE_ENV=development
    networks:
      - app-network
  mongo:
    image: mongo
    volumes:
      - data-volume:/data/db
    ports:
      - "27017:27017"
    networks:
      - app-network
  client:
    build:
      context: ./client
      dockerfile: Dockerfile
    image: ceddit-client
    container_name: ceddit-react-client
    command: npm start
    volumes:
      - ./client/:/usr/app
      - /usr/app/node_modules
    depends_on:
      - server
    ports:
      - "3000:3000"
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  data-volume:
  node_modules:
  web-root:
    driver: local
