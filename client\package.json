{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@chakra-ui/icons": "^2.1.1", "@chakra-ui/react": "^2.8.2", "@cloudinary/react": "^1.11.2", "@cloudinary/url-gen": "^1.14.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@reduxjs/toolkit": "^1.9.7", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "cloudinary-react": "^1.8.1", "dotenv": "^16.3.1", "framer-motion": "^10.16.5", "moment": "^2.29.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "react-infinite-scroll-component": "^6.1.0", "react-redux": "^8.1.3", "react-router-dom": "^6.19.0", "react-scripts": "5.0.1", "socket.io-client": "^4.7.2", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.3.5"}}