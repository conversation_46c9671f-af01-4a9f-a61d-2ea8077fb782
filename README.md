<p align="center">
  <a href="https://ceddit-one.vercel.app/">
    <img alt="ceddit" src="./client/public/logo.png" width="125" />
  </a>
</p>
<h1 align="center">Ceddit✨️</h1>

<p align="center">
<a href="https://github.com/Devansh-365/ceddit/blob/master/LICENSE" target="blank">
<img src="https://img.shields.io/github/license/Devansh-365/ceddit?style=flat-square" alt="ceddit licence" />
</a>
<a href="https://github.com/Devansh-365/ceddit/fork" target="blank">
<img src="https://img.shields.io/github/forks/Devansh-365/ceddit?style=flat-square" alt="ceddit forks"/>
</a>
<a href="https://github.com/Devansh-365/ceddit/stargazers" target="blank">
<img src="https://img.shields.io/github/stars/Devansh-365/ceddit?style=flat-square" alt="ceddit stars"/>
</a>
<a href="https://github.com/Devansh-365/ceddit/issues" target="blank">
<img src="https://img.shields.io/github/issues/Devansh-365/ceddit?style=flat-square" alt="ceddit issues"/>
</a>
<a href="https://github.com/Devansh-365/ceddit/pulls" target="blank">
<img src="https://img.shields.io/github/issues-pr/Devansh-365/ceddit?style=flat-square" alt="ceddit pull-requests"/>
</a>
</p>

<!-- <p align="center"><img src="public/sc.png" alt="ceddit sc" width="550" /></p> -->

<p align="center">
    <a href="https://ceddit-one.vercel.app/" target="blank">View Demo</a>
    ·
    <a href="https://github.com/Devansh-365/ceddit/issues/new/choose">Report Bug</a>
    ·
    <a href="https://github.com/Devansh-365/ceddit/issues/new/choose">Request Feature</a>
</p>

### Introducing ceddit ✌️

A dynamic platform for developers: fostering collaborative learning through programming question posting and knowledge sharing.

## 🚀 Demo

<a href="https://ceddit-one.vercel.app/" target="blank">
<!-- <img src="public/sc.png" /> -->
</a>

Try the App: [ceddit](https://ceddit-one.vercel.app/)

## 🧐 Features

A dynamic platform for developers: fostering collaborative learning through programming question posting and knowledge sharing.

- **Beautiful UI using TailwindCSS and ChakraUI**
- **User Authentication: Users can authenticate using their email**
- **Community Creation: Users can create their own communities**
- **Posting: Users can create posts within their communities**
- **Post Viewing & Sorting: Users can view posts and sort them by time, likes, and comments**
- **Voting: Users can upvote and downvote posts and comments**
- **Commenting: Users can comment on posts and their own comments**
- **Skeleton Loading: Implement skeleton loading feature for better user experience**
- **Full responsivity and mobile UI**

## 🛠️ Installation Steps

1. Clone the repository

```bash
git clone https://github.com/Devansh-365/ceddit.git
```

2. Change the working directory

```bash
cd ceddit
```

3. Setup with docker

```bash
docker-compose up
```

<!-- 4. Copy .env.example to .env.local and update the variables.

```bash
cp .env.example .env.local
``` -->

<!-- 5. Run the app

```bash
npm run start
``` -->

You are all set! Open [localhost:3000](http://localhost:3000/) to see the app.

## 💻 Built with

- [React JS](https://react.dev/): javascript libraray
- [Javascript](): language
- [JWT Auth](https://jwt.io/): for authentication
- [Chakra UI](https://chakra-ui.com/): for components
- [React Icons](https://react-icons.github.io/react-icons/): for icons
- [MongoDb](https://www.mongodb.com/): for database
- [NodeJs](https://nodejs.org/en/): for backend
- [Exprees](https://expressjs.com/): for backend
- [react-hot-toast](https://react-hot-toast.com/): for toasts
- [Docker](https://www.docker.com/)
- [Vercel](http://vercel.com/): for hosting

## 🛡️ License

This project is licensed under the MIT License - see the [`LICENSE`](LICENSE) file for details.

## 🍰 Contributing

- ceddit is an open-source project and we welcome contributions from the community.

- If you'd like to contribute, please fork the repository and make changes as you'd like. Pull requests are warmly welcome.

### Our Contributors ✨

<a href="https://github.com/Devansh-365/ceddit/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=Devansh-365/ceddit" />
</a>

---

<h3 align="center">
Ceddit needs a ⭐️ from you
</h3>
