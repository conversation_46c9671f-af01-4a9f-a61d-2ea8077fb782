const jwt = require("jsonwebtoken");

const verifyToken = (req, res, next) => {
  try {
    const token = req.headers["x-access-token"];

    if (!token) {
      return res.status(401).json({
        error: "No token provided",
        code: "NO_TOKEN"
      });
    }

    // Verify the token
    const decoded = jwt.verify(token, process.env.TOKEN_KEY);
    const { userId, isAdmin } = decoded;

    req.user = {
      ...req.body,
      userId,
      isAdmin,
    };

    return next();
  } catch (err) {
    // Handle specific JWT errors
    if (err.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: "Invalid token. Please log in again.",
        code: "INVALID_TOKEN"
      });
    } else if (err.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: "Token expired. Please log in again.",
        code: "TOKEN_EXPIRED"
      });
    } else {
      return res.status(401).json({
        error: "Authentication failed. Please log in again.",
        code: "AUTH_FAILED"
      });
    }
  }
};

module.exports = { verifyToken };
