{"name": "ceddit", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon app.js"}, "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "body-parser": "^1.20.2", "cors": "^2.8.5", "curl": "^0.1.4", "dotenv": "^16.3.1", "express": "^4.18.2", "express-session": "^1.17.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.1", "nodemon": "^3.0.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "socket.io": "^4.7.2"}, "description": ""}